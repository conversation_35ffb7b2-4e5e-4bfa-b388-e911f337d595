<template>
  <v-navigation-drawer
    app
    permanent
    color="#212121"
    dark
  >
    <v-list dense>
      <v-list-item @click="$router.push('/dashboard')" class="sidebar-item">
        <v-list-item-icon>
          <v-icon color="#F7B733">mdi-view-dashboard</v-icon>
        </v-list-item-icon>
        <v-list-item-content>
          <v-list-item-title class="sidebar-title">Dashboard</v-list-item-title>
        </v-list-item-content>
      </v-list-item>

      <v-list-item @click="$router.push('/users')" class="sidebar-item">
        <v-list-item-icon>
          <v-icon color="#F7B733">mdi-account-multiple</v-icon>
        </v-list-item-icon>
        <v-list-item-content>
          <v-list-item-title class="sidebar-title">Users</v-list-item-title>
        </v-list-item-content>
      </v-list-item>

      <v-list-item @click="$router.push('/ussd')" class="sidebar-item">
        <v-list-item-icon>
          <v-icon color="#F7B733">mdi-telephone</v-icon>
        </v-list-item-icon>
        <v-list-item-content>
          <v-list-item-title class="sidebar-title">USSD Management</v-list-item-title>
        </v-list-item-content>
      </v-list-item>

      <v-list-item @click="$router.push('/reports')" class="sidebar-item">
        <v-list-item-icon>
          <v-icon color="#F7B733">mdi-file-chart</v-icon>
        </v-list-item-icon>
        <v-list-item-content>
          <v-list-item-title class="sidebar-title">Reports</v-list-item-title>
        </v-list-item-content>
      </v-list-item>

      <v-list-item @click="$router.push('/settings')" class="sidebar-item">
        <v-list-item-icon>
          <v-icon color="#F7B733">mdi-cog</v-icon>
        </v-list-item-icon>
        <v-list-item-content>
          <v-list-item-title class="sidebar-title">Settings</v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </v-navigation-drawer>
</template>

<script>
export default {
  name: "AdminSidebar"
};
</script>

<style scoped>
.sidebar-item {
  transition: background-color 0.3s;
}

.sidebar-item:hover {
  background-color: #424242;
}

.sidebar-title {
  color: #ffffff;
  font-weight: bold;
}

.v-navigation-drawer {
  border-right: 1px solid #424242;
}
</style>
