<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card class="bid-card">
          <v-card-title class="bid-card-title">
            <span class="text-h5">Bid History Report</span>
            <v-spacer></v-spacer>
            <v-text-field v-model="search" label="Search by Name" outlined dense hide-details class="search-field"
              color="yellow darken-2"></v-text-field>
            <v-btn class="filter-btn" @click="filterBids">Filter</v-btn>
            <v-btn class="export-btn" @click="exportReport">Export Report</v-btn>
            <v-btn class="refresh-btn" @click="refreshBids">Refresh</v-btn>
          </v-card-title>
          <v-data-table :headers="headers" :items="filteredBids" class="elevation-1 bid-data-table" dense>
            <template v-slot:[`item.amount`]="slotProps">
              <v-chip :color="'green darken-2'" dark>
                {{ formatCurrency(slotProps.item.amount) }}
              </v-chip>
            </template>
            <template v-slot:[`item.receivedDate`]="slotProps">
              <span>
                {{ formatDateTime(slotProps.item.receivedDate) }}
              </span>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { getVehicleBids } from '@/services/vehicleService'
export default {
  data() {
    return {
      search: "",
      headers: [
        { text: "Reg Number", value: "registrationNumber" },
        { text: "Make", value: "model" },
        { text: "Color", value: "colour" },
        { text: "Current Bid", value: "amount" },
        { text: "Bid Date", value: "receivedDate" },
        { text: "Winner", value: "winner" },
        { text: "Phone Number", value: "personMobilePhone" },

      ],
      bids: [],
      filteredBids: [],
    };
  },
  created() {
    this.getBids()
  },
  methods: {
    getBids(query = '') {
      getVehicleBids(query).then((response) => {
        const bids = response.data.bids;

        const savedUniqueId = [];
        // remove duplicates using Id
        const uniqueBids = [];

        for (let i of bids) {
          if (savedUniqueId.includes(i.Id)) {
            continue;
          }
          savedUniqueId.push(i.Id);
          uniqueBids.push(i);
        }

        // format and add winner key
        this.filteredBids = uniqueBids.map((bid) => {
          return {
            ...bid,
            winner: bid.personFirstName + ' ' + bid.personLastName,
          }
        });
      });
    },
    filterBids() {
      if (this.search.length) {
        this.getBids(this.search);
      }
    },
    refreshBids() {
      this.search = ""; // Clear search input
      this.filteredBids = [...this.bids];
    },
    exportReport() {
      const csvContent =
        "data:text/csv;charset=utf-8," +
        this.bids.map((bid) => Object.values(bid).join(",")).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "bid_history_report.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    formatCurrency(value) {
      return new Intl.NumberFormat('en-TZ', {
        style: 'currency',
        currency: 'TZS'
      }).format(value);
    },

    formatDateTime(value) {
      if (!value) return 'No Date';
      return new Date(value).toLocaleString();
    },
  }
};
</script>

<style scoped>
.bid-card {
  background-color: #212121;
  color: #ffffff;
  border-radius: 20px;
  padding: 20px;
}

.bid-card-title {
  display: flex;
  align-items: center;
  color: #f7b733;
}

.v-card-title>.v-btn {
  margin-left: 10px;
}

.export-btn {
  background-color: #00bfa5 !important;
  /* Green color */
  color: white !important;
  padding: 8px 16px;
  margin-right: 10px;
  text-transform: none;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
  border-radius: 25px;
  /* Rounded edges */
}

.filter-btn {
  background-color: #e53935 !important;
  /* Red color */
  color: white !important;
  padding: 8px 16px;
  margin-right: 10px;
  text-transform: none;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
  border-radius: 25px;
  /* Rounded edges */
}

.refresh-btn {
  background-color: #3949ab !important;
  /* Blue color */
  color: white !important;
  padding: 8px 16px;
  margin-right: 10px;
  text-transform: none;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
  border-radius: 25px;
  /* Rounded edges */
}

.bid-data-table {
  /* background-color: #333333; */
  border-radius: 10px;
}

.v-chip {
  font-weight: bold;
}
</style>
