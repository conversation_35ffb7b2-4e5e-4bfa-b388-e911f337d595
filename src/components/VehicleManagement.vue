<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card class="vehicle-card">
          <v-card-title class="vehicle-card-title">
            Vehicle Management
            <v-spacer></v-spacer>
            <v-btn class="new-vehicle-btn" @click="openDialog">New Vehicle</v-btn>
          </v-card-title>
          <v-data-table :headers="headers" :items="vehicles" class="elevation-1 vehicle-data-table">
            <template v-slot:[`item.actions`]="{ item }">
              <v-btn icon class="edit-btn" @click="editVehicle(item)">
                <v-icon>mdi-pencil</v-icon>
              </v-btn>
              <v-btn icon class="delete-btn" @click="deleteVehicle(item.id)">
                <v-icon>mdi-delete</v-icon>
              </v-btn>

              <v-btn title="Add to auction" icon class="success" @click="openAuctionDialog(item)">
                <v-icon>mdi-plus</v-icon>
              </v-btn>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>

    <!-- Vehicle Dialog -->
    <v-dialog v-model="dialog" max-width="600px">
      <v-card class="dialog-card">
        <v-card-title class="dialog-title">
          <span>{{ editing ? "Edit Vehicle" : "New Vehicle" }}</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-text-field v-model="vehicle.registrationNumber" label="Registration Number" outlined
              color="yellow darken-2" required></v-text-field>
            <v-text-field v-model="vehicle.make" label="Make" outlined color="yellow darken-2" required></v-text-field>
            <v-text-field v-model="vehicle.model" label="Model" outlined color="yellow darken-2"
              required></v-text-field>
            <v-text-field v-model="vehicle.year" label="Year Of Manufacture" outlined color="yellow darken-2"
              type="number" required></v-text-field>
            <v-text-field v-model="vehicle.engineNumber" label="Engine Number" outlined color="yellow darken-2"
              required></v-text-field>
            <v-text-field v-model="vehicle.vehicleIdentificationNumber" label="Vehicle Identification Number" outlined
              color="yellow darken-2" required></v-text-field>
            <v-text-field v-model="vehicle.chassisNumber" label="Chassis Number" outlined color="yellow darken-2"
              required></v-text-field>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="cancel-btn" text @click="closeDialog">Cancel</v-btn>
          <v-btn class="save-btn" text @click="saveVehicle">
            {{ editing ? "Save" : "Create" }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Auction Dualog -->
    <v-dialog v-model="auctionDialog" max-width="600px">
      <v-card class="dialog-card">
        <v-card-title class="dialog-title">
          <span>Add to auction</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <!-- <v-text-field outlined color="yellow darken-2"> -->
            <input type="datetime" v-model="auction.startDate" label="Start Date" color="yellow darken-2" />
            <!-- </v-text-field> -->
            <input type="datetime" v-model="auction.endDate" label="End Date" color="yellow darken-2" required />
            <v-text-field v-model="auction.reservePrice" label="Reserve Price" outlined color="yellow darken-2"
              required></v-text-field>
            <v-text-field v-model="auction.bidIncrement" label="Bid increment" outlined color="yellow darken-2"
              required></v-text-field>

            <v-text-field v-model="auction.minimumBid" label="Minimum Bid" outlined color="yellow darken-2"
              required></v-text-field>

            <v-text-field v-model="auction.targetBid" label="Target Bid" outlined color="yellow darken-2"
              required></v-text-field>

            <v-text-field v-model="auction.colour" label="Color" outlined color="yellow darken-2"
              required></v-text-field>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="cancel-btn" text @click="closeAuctionDialog">Cancel</v-btn>
          <v-btn class="save-btn" text @click="saveAuction">
            {{ "Create" }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import {
  getVehicles,
  createVehicle,
  deleteVehicle,
  updateVehicle,
  createAuction,
} from "@/services/vehicleService";



import { v4 as uuidv4 } from "uuid";
import { successAlert, errorAlert } from "@/utils/swal";

export default {
  name: "VehicleManagement",
  data() {
    return {
      dialog: false,
      auctionDialog: false,
      auction: {
        startDate: "",
        endDate: "",
        reservePrice: "",
        bidIncrement: "",
        minimumBid: "",
        color: "",
      },
      editing: false,
      vehicle: this.getDefaultVehicle(),
      selectedVehicle: null,
      vehicles: [],
      headers: [
        { text: "Registration Number", value: "registrationNumber" },
        { text: "Make", value: "make" },
        { text: "Model", value: "model" },
        { text: "YOM", value: "year" },
        { text: "Status", value: "status" },
        { text: "Actions", value: "actions", sortable: false },
      ],
    };
  },
  methods: {
    getDefaultVehicle() {
      return {
        id: uuidv4(),
        make: "",
        model: "",
        year: "",
        registrationNumber: "",
        status: "FANIKIWA_BIDDING",
        engineNumber: "",
        vehicleIdentificationNumber: "",
        chassisNumber: "",
        "manufatureYear": "",
        "categoryId": "1ab71a9c-f5bf-4876-9aec-ab654635d740"
      };
    },
    openDialog() {
      this.vehicle = this.getDefaultVehicle();
      this.editing = false;
      this.dialog = true;
    },
    openAuctionDialog(item) {
      this.selectedVehicle = item;
      console.log("Selected vehicle:", item);

      this.auctionDialog = true;
    },
    closeDialog() {
      this.dialog = false;
    },
    closeAuctionDialog() {
      this.auctionDialog = false;
    },
    async saveVehicle() {
      try {
        console.log("Saving vehicle:", this.vehicle);
        let response;
        if (this.editing) {
          if (!this.vehicle.Id) {
            throw new Error("Vehicle ID is missing for the update operation");
          }
          response = await updateVehicle(this.vehicle);
        } else {
          this.vehicle.manufatureYear = this.vehicle.year;
          response = await createVehicle(this.vehicle);
          console.log(response);
        }

        if (response && response.result.success) {
          console.log("Vehicle saved successfully");
          successAlert("Vehicle saved successfully");
        } else {
          errorAlert("Failed to save the vehicle");
          console.log(
            "Vehicle save operation returned an unexpected result:",
            response.data
          );
        }

        // await this.loadVehicles();
        this.closeDialog();
      } catch (error) {
        console.error("Failed to save the vehicle:", error);
        errorAlert("Failed to save the vehicle");
        if (
          error.response &&
          error.response.data &&
          error.response.data.success
        ) {
          console.log("Operation succeeded with errors:", error.response.data);
          await this.loadVehicles();
          this.closeDialog();
        }
      }
    },
    async saveAuction() {
      createAuction({
        vehicleId: this.selectedVehicle.Id,
        startDate: this.auction.startDate || new Date(),
        endDate: this.auction.endDate || new Date(),
        reservePrice: this.auction.reservePrice,
        bidIncrement: this.auction.bidIncrement,
        minimumBid: this.auction.minimumBid,
        colour: this.auction.colour,
        targetBid: this.auction.targetBid,
        model: this.selectedVehicle.model,
      })
        .then((response) => {
          console.log("Auction created successfully:", response);
          successAlert("Auction created successfully");
          this.closeAuctionDialog();
        })
        .catch((error) => {
          console.error(error.response);
          errorAlert(error.response && error.response.data.error || "Failed to create auction");
        });
    },
    editVehicle(item) {
      console.log("Editing vehicle:", item);
      this.vehicle = { ...item };
      this.editing = true;
      this.dialog = true;
    },
    async deleteVehicle(id) {
      try {
        if (!id) {
          throw new Error("Vehicle ID is missing for the delete operation");
        }
        console.log("Deleting vehicle with id:", id);
        await deleteVehicle(id);
        await this.loadVehicles();
      } catch (error) {
        console.error("Failed to delete the vehicle:", error);
      }
    },
    async loadVehicles() {
      try {
        console.log("Loading vehicles...");
        const response = await getVehicles();
        console.log("API Response:", response);
        this.vehicles = response.data && response.data.vehicles || [];
        console.log("Loaded vehicles:", this.vehicles);
      } catch (error) {
        console.error("Error loading vehicles:", error);
        this.vehicles = [];
      }
    },
  },
  mounted() {
    console.log("Component mounted, loading vehicles...");
    this.loadVehicles();
  },
};
</script>

<style scoped>
.vehicle-card {
  background-color: #212121;
  color: #ffffff;
  border-radius: 20px;
  padding: 20px;
}

.vehicle-card-title {
  display: flex;
  align-items: center;
  color: #f7b733;
}

.new-vehicle-btn {
  background-color: #f7b733 !important;
  color: #212121 !important;
  border-radius: 25px;
  padding: 8px 16px;
}

.vehicle-data-table {
  border-radius: 10px;
}

.edit-btn {
  color: #f7b733;
}

.delete-btn {
  color: #e53935;
}

.dialog-card {
  color: #ffffff;
}

.dialog-title {
  color: #0a0a0a;
}

.cancel-btn {
  background-color: #e53935;
  color: #ffffff;
}

.save-btn {
  background-color: #f7b733;
  color: #212121;
}
</style>
