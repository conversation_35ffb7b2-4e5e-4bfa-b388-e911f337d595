<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card class="product-card">
          <v-card-title class="product-card-title">
            <span class="text-h5">Auction</span>
            <v-spacer></v-spacer>
            <v-text-field v-model="search" label="Search by Name" @input="filterProducts" outlined dense hide-details
              class="search-field" color="yellow darken-2"></v-text-field>
            <v-btn class="filter-btn" @click="filterProducts">Filter</v-btn>
            <v-btn class="refresh-btn" @click="refreshProducts">Refresh</v-btn>
          </v-card-title>
          <v-data-table :headers="headers" :items="filteredProducts" class="elevation-1 product-data-table">
            <template v-slot:[`item.reservePrice`]="slotProps">
              <span>{{ formatNumber(slotProps.item.reservePrice) }}</span>
            </template>

            <template v-slot:[`item.minimumBid`]="slotProps">
              <span>{{ formatNumber(slotProps.item.minimumBid) }}</span>
            </template>

            <template v-slot:[`item.bidIncrement`]="slotProps">
              <span>{{ formatNumber(slotProps.item.bidIncrement) }}</span>
            </template>

            <template v-slot:[`item.startDate`]="slotProps">
              <span>{{ formatDate(slotProps.item.startDate) }}</span>
            </template>

            <template v-slot:[`item.endDate`]="slotProps">
              <span>{{ formatDate(slotProps.item.endDate) }}</span>
            </template>


            <template v-slot:[`item.actions`]="slotProps">
              <v-btn icon @click="editProduct(slotProps.item)" class="edit-btn">
                <v-icon>mdi-pencil</v-icon>
              </v-btn>
              <v-btn icon @click="deleteProduct(slotProps.item)" class="delete-btn">
                <v-icon>mdi-delete</v-icon>
              </v-btn>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>

    <!-- Add/Edit Product Dialog -->
    <v-dialog v-model="showDialog" max-width="800px">
      <v-card class="dialog-card">
        <v-card-title class="headline">{{
          editing ? "Edit Product" : "Create Product"
          }}</v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col cols="12" sm="6">
                <v-select v-model="editedProduct.catalogue" :items="catalogues" label="Auction Catalogue" outlined
                  color="yellow darken-2"></v-select>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field v-model="editedProduct.identifier" label="Product unique identifier" outlined
                  color="yellow darken-2"></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field v-model="editedProduct.price" label="Product Starting Price" outlined
                  color="yellow darken-2"></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-select v-model="editedProduct.make" :items="vehicleMakes" label="Vehicle Make" outlined
                  color="yellow darken-2"></v-select>
              </v-col>
              <v-col cols="12" sm="6">
                <v-select v-model="editedProduct.model" :items="vehicleModels" label="Vehicle Model" outlined
                  color="yellow darken-2"></v-select>
              </v-col>
              <v-col cols="12" sm="6">
                <v-select v-model="editedProduct.location" :items="locations" label="Location" outlined
                  color="yellow darken-2"></v-select>
              </v-col>
              <v-col cols="12" sm="6">
                <v-select v-model="editedProduct.color" :items="colors" label="Color" outlined
                  color="yellow darken-2"></v-select>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field v-model="editedProduct.ownersInterest" label="Owners Interest" outlined
                  color="yellow darken-2"></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field v-model="editedProduct.yom" label="YOM" outlined color="yellow darken-2"></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-file-input v-model="editedProduct.photos" label="Upload Asset Photos" accept="image/*" outlined
                  color="yellow darken-2"></v-file-input>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn class="clear-btn" @click="clearForm">Clear</v-btn>
          <v-btn class="save-btn" @click="saveProduct">
            {{ editing ? "Save Changes" : "Create Product" }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>

import { getAuctionProducts } from '@/services/vehicleService';

export default {
  data() {
    return {
      search: "",
      headers: [
        { text: "Reg No", value: "registrationNumber" },
        { text: "Tag", value: "category" },
        { text: "Make", value: "make" },
        { text: "Model", value: "model" },
        { text: "Reserve Price", value: "reservePrice" },
        { text: "Minimum Bid", value: "minimumBid" },
        { text: "Bid Increment", value: "bidIncrement" },
        { text: "Auction Startdate", value: "startDate" },
        { text: "Auction Startdate", value: "endDate" },
        { text: "Actions", value: "actions", sortable: false },
      ],
      products: [],
      showDialog: false,
      editing: false,
      editedProduct: {
        id: null,
        name: "",
        price: "",
        identifier: "",
        catalogue: "",
        make: "",
        model: "",
        location: "",
        color: "",
        ownersInterest: "",
        yom: "",
        photos: "",
      },
      filteredProducts: [],
      catalogues: ["Equity August Catalogue"],
      vehicleMakes: ["Nissan", "Toyota", "Honda", "Mercedes"],
      vehicleModels: [
        "Juke",
        "Xtrail",
        "Civic",
        "Actross",
        "Ractis",
        "Trailer",
      ],
      locations: ["Nairobi", "Mombasa", "Kisumu"],
      colors: ["Red", "Blue", "Black"],
    };
  },
  created() {
    getAuctionProducts().then((response) => {
      this.products = response.data.vehicles;
      this.refreshProducts();
    });
  },
  methods: {
    getImagePath(imageName) {
      try {
        return require(`@/assets/${imageName}`);
      } catch (error) {
        console.error(`Image not found: ${imageName}`, error);
        return "";
      }
    },
    filterProducts() {
      if (this.search) {
        this.filteredProducts = this.products.filter((product) =>
          product.name.toLowerCase().includes(this.search.toLowerCase())
        );
      } else {
        this.filteredProducts = this.products;
      }
    },
    refreshProducts() {
      this.filteredProducts = [...this.products];
    },
    clearForm() {
      this.editedProduct = {
        id: null,
        name: "",
        price: "",
        identifier: "",
        catalogue: "",
        make: "",
        model: "",
        location: "",
        color: "",
        ownersInterest: "",
        yom: "",
        photos: "",
      };
      this.editing = false;
    },
    editProduct(item) {
      this.editedProduct = { ...item };
      this.editing = true;
      this.showDialog = true;
    },
    saveProduct() {
      if (this.editing) {
        const index = this.products.findIndex(
          (p) => p.id === this.editedProduct.id
        );
        if (index !== -1) {
          this.products.splice(index, 1, { ...this.editedProduct });
        }
      } else {
        this.editedProduct.id = this.products.length + 1;
        this.products.push({ ...this.editedProduct });
      }
      this.refreshProducts();
      this.closeDialog();
    },
    deleteProduct(item) {
      if (confirm(`Are you sure you want to delete ${item.name}?`)) {
        const index = this.products.findIndex((p) => p.id === item.id);
        if (index !== -1) {
          this.products.splice(index, 1);
        }
        this.refreshProducts();
      }
    },
    closeDialog() {
      this.showDialog = false;
      this.clearForm();
    },
    formatNumber(num) {
      if (!num) return 0;
      return new Intl.NumberFormat().format(num);
    },
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleString();
    },
  },
};
</script>

<style scoped>
.product-card {
  background-color: #212121;
  color: #ffffff;
  border-radius: 20px;
  padding: 20px;
}

.product-card-title {
  display: flex;
  align-items: center;
  color: #f7b733;
}

.search-field {
  background-color: #424242 !important;
  border-radius: 10px;
  color: #ffffff;
  --v-theme-primary: #f7b733;
  /* Ensure primary color is correct */
}

.filter-btn,
.refresh-btn {
  background-color: #f7b733 !important;
  color: #212121 !important;
  margin-left: 10px;
  border-radius: 25px;
}

.product-data-table {
  /* background-color: #333333; */
  border-radius: 10px;
}

.edit-btn {
  color: #f7b733;
}

.delete-btn {
  color: #e53935;
}

.dialog-card {
  /* background-color: #212121; */
  color: #212121;
}

.clear-btn {
  background-color: #e53935;
  color: #212121;
}

.save-btn {
  background-color: #f7b733;
  color: #212121;
}
</style>
