<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-app-bar app color="#212121" dark>
          <v-toolbar-title class="dashboard-title"
            >Admin Dashboard</v-toolbar-title
          >
          <v-spacer></v-spacer>
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn icon v-bind="attrs" v-on="on" class="avatar-btn">
                <v-avatar>
                  <v-icon color="#f7b733">mdi-account-circle</v-icon>
                </v-avatar>
              </v-btn>
            </template>
            <v-list>
              <v-list-item>
                <v-list-item-content>
                  <v-list-item-title>{{ username }}</v-list-item-title>
                  <v-list-item-subtitle>Admin</v-list-item-subtitle>
                </v-list-item-content>
              </v-list-item>
              <v-list-item @click="logout">
                <v-list-item-icon>
                  <v-icon color="#e53935">mdi-logout</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>Logout</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-app-bar>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" sm="4">
        <v-card class="dashboard-card" @click="$router.push('/products')">
          <v-card-title class="dashboard-card-title"
            >Manage Auction</v-card-title
          >
          <v-card-text> Click here to manage Auction. </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" sm="4">
        <v-card class="dashboard-card" @click="$router.push('/bids')">
          <v-card-title class="dashboard-card-title">Manage Bids</v-card-title>
          <v-card-text> Click here to manage bids. </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" sm="4">
        <v-card class="dashboard-card" @click="$router.push('/vehicles')">
          <v-card-title class="dashboard-card-title"
            >Manage Vehicles</v-card-title
          >
          <v-card-text> Click here to manage vehicles. </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  name: "AdminDashboard",
  data() {
    return {
      username: localStorage.getItem("username") || "Admin",
    };
  },
  methods: {
    logout() {
      localStorage.removeItem("authenticated");
      localStorage.removeItem("username");
      this.$router.push("/");
    },
  },
};
</script>

<style scoped>
.dashboard-title {
  color: #f7b733;
}

.avatar-btn .v-icon {
  color: #f7b733;
}

.v-card {
  cursor: pointer;
}

.dashboard-card {
  /* background-color: #333333; */
  color: #ffffff;
  border-radius: 15px;
  padding: 20px;
  transition: background-color 0.3s ease;
}

.dashboard-card:hover {
  background-color: #424242;
}

.dashboard-card-title {
  color: #f7b733;
  font-weight: bold;
}
</style>
