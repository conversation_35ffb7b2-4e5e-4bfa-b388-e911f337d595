<template>
    <v-container fluid>
        <v-row>
            <v-col cols="12">
                <v-card class="product-card">
                    <v-card-title class="product-card-title">
                        <span class="text-h5">Loans</span>
                        <v-spacer></v-spacer>
                        <v-text-field v-model="search" label="Search by Name" @input="filterProducts" outlined dense
                            hide-details class="search-field" color="yellow darken-2"></v-text-field>
                        <v-btn class="filter-btn" @click="filterProducts">Filter</v-btn>
                        <v-btn class="refresh-btn" @click="refreshProducts">Refresh</v-btn>
                    </v-card-title>
                    <v-data-table :headers="headers" :items="filteredProducts" class="elevation-1 product-data-table">
                        <template v-slot:[`item.loanTenure`]="slotProps">
                            <span>{{ formatTenure(slotProps.item.tenure) }}</span>
                        </template>

                        <template v-slot:[`item.status`]="slotProps">
                            <span :class="slotProps.item.status == 'SUCCESS' ? 'success' : 'failed'">{{
                                formatTenure(slotProps.item.status)
                                }}</span>
                        </template>

                        <template v-slot:[`item.createdAt`]="slotProps">
                            <span>{{ formatDate(slotProps.item.createdAt) }}</span>
                        </template>

                        <template v-slot:[`item.updatedAt`]="slotProps">
                            <span>{{ formatDate(slotProps.item.updatedAt) }}</span>
                        </template>
                    </v-data-table>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>

import { getLoan } from '../services/leads';
export default {
    data() {
        return {
            search: '',
            headers: [
                { text: 'Account Number', value: 'accountNumber' },
                { text: 'Amount', value: 'loanAmount' },
                { text: 'Loan Name', value: 'loanName' },
                { text: 'Product', value: 'product' },
                { text: 'Loan Amount', value: 'amount' },
                { text: 'Loan Tenure', value: 'tenure' },
                { text: 'Status', value: 'status' },
                { text: 'Guarantor Name', value: 'gurantor' },
                { text: 'Guarantor Phone', value: 'gurantorPhoneNo' },
                { text: 'Type', value: 'loanType' },
                { text: 'Usage', value: 'loanUsage' },
                { text: 'Purpose', value: 'loanPurpose' },
                { text: 'Date Created', value: 'createdAt' },
                { text: 'Date Updated', value: 'updatedAt' },
            ],
            products: [],
            showDialog: false,
            filteredProducts: [],
            catalogues: ['Equity August Catalogue'],
            vehicleMakes: ['Nissan', 'Toyota', 'Honda', 'Mercedes'],
            vehicleModels: ['Juke', 'Xtrail', 'Civic', 'Actross', 'Ractis', 'Trailer'],
            locations: ['Nairobi', 'Mombasa', 'Kisumu'],
            colors: ['Red', 'Blue', 'Black']
        };
    },
    created() {
        this.filteredProducts = this.products;
    },
    methods: {
        formatDate(date) {
            return new Date(date).toLocaleString();
        },
        formatTenure(tenure) {
            if (typeof tenure === 'number') {
                return `${tenure} months`;
            }
            return tenure;
        },
        filterProducts() {
            if (this.search) {
                this.filteredProducts = this.products.filter(product =>
                    product.name.toLowerCase().includes(this.search.toLowerCase())
                );
            } else {
                this.filteredProducts = this.products;
            }
        },
        refreshProducts() {
            this.filteredProducts = [...this.products];
        },
        fetchLeads() {
            // Fetch leads from the API
            getLoan()
                .then(response => {
                    const fields = ['accountNumber', 'loanAmount', 'loanName', 'product', 'amount', 'tenure', 'status', 'gurantor', 'gurantorPhoneNo', 'loanType', 'loanUsage', 'loanPurpose', 'createdAt', 'updatedAt'];
                    this.products = response.data.map(product => {
                        fields.forEach(field => {
                            if (!product[field]) {
                                product[field] = 'n/a';
                            }
                        });
                        return {
                            ...product,
                        };

                    });
                    this.filteredProducts = this.products;
                })
                .catch(error => {
                    console.error(error);
                });
        },
    },
    mounted() {
        this.fetchLeads();
    }
};
</script>

<style scoped>
.product-card {
    background-color: #212121;
    color: #ffffff;
    border-radius: 20px;
    padding: 20px;
}

.product-card-title {
    display: flex;
    align-items: center;
    color: #f7b733;
}

.search-field {
    background-color: #424242 !important;
    border-radius: 10px;
    color: #ffffff;
    --v-theme-primary: #f7b733;
    /* Ensure primary color is correct */
}

.filter-btn,
.refresh-btn {
    background-color: #f7b733 !important;
    color: #212121 !important;
    margin-left: 10px;
    border-radius: 25px;
}

.product-data-table {
    /* background-color: #333333; */
    border-radius: 10px;
}

.edit-btn {
    color: #f7b733;
}

.delete-btn {
    color: #e53935;
}

.dialog-card {
    /* background-color: #212121; */
    color: #212121;
}

.clear-btn {
    background-color: #e53935;
    color: #212121;
}

.save-btn {
    background-color: #f7b733;
    color: #212121;
}

.success {
    background: green;
    padding: 8px;
    color: white;
    border-radius: 5px;
}

.failed {
    background: red;
    padding: 8px;
    color: white;
    border-radius: 5px;
}
</style>