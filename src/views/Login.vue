<template>
  <v-container class="login-container" fluid fill-height>
    <v-row align="center" justify="center">
      <!-- Left Side: Logo -->
      <v-col cols="12" sm="8" md="2" class="logo-col">
        <img src="https://www.platcorpgroup.com/media/qrjot3do/fanikwa-sub.png" alt="Fanikwa Logo" class="login-logo" />
      </v-col>

      <!-- Right Side: Login Card -->
      <v-col cols="12" sm="8" md="4" class="login-col">
        <v-card class="login-card">
          <v-card-title class="headline login-title">Admin Login</v-card-title>
          <v-card-text>
            <v-form>
              <v-text-field label="Username" v-model="username" prepend-icon="mdi-account" outlined class="login-field"
                color="yellow darken-2"></v-text-field>
              <v-text-field label="Password" v-model="password" prepend-icon="mdi-lock" type="password" outlined
                class="login-field" color="yellow darken-2"></v-text-field>
              <v-btn :loading="loading" block class="login-button" @click="login">
                Login
              </v-btn>
              <v-alert v-if="error" type="error" dense class="mt-4">
                {{ error }}
              </v-alert>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  name: "AdminLogin",
  data() {
    return {
      username: "",
      password: "",
      loading: false,
      error: null,
    };
  },
  methods: {
    login() {
      this.loading = true;
      this.error = null;

      // Simulate a login process
      setTimeout(() => {
        this.loading = false;
        if (
          this.username === '' &&
          this.password === ''
        ) {
          // Store authentication flag and username in localStorage
          localStorage.setItem("authenticated", true);
          localStorage.setItem("username", this.username); // Store the username
          this.$router.push("/dashboard");
        } else {
          this.error = "Invalid username or password";
        }
      }, 1000);
    },
  },
};
</script>

<style scoped>
.login-container {
  background: linear-gradient(to right, #f7b733, #fc4a1a);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-card {
  border-radius: 20px;
  background-color: #212121;
  padding: 30px;
  color: #ffffff;
}

.login-title {
  justify-content: center;
  color: #f7b733;
  font-weight: bold;
}

.login-field {
  margin-bottom: 20px;
  color: #ffffff;
}

.login-button {
  background-color: #f7b733 !important;
  color: #ffffff !important;
  border-radius: 50px;
}

.v-text-field--outlined .v-input__control .v-input__slot {
  background-color: #424242 !important;
  border-color: #f7b733 !important;
  color: #ffffff !important;
}

.v-input__append-inner .v-icon {
  color: #f7b733 !important;
}

.v-alert {
  background-color: #d32f2f;
  color: #ffffff;
}

.v-application .v-application--is-ltr .v-application--wrap {
  background-color: transparent !important;
}

.logo-col {
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-logo {
  max-width: 100%;
  height: auto;
}
</style>
