<template>
    <v-container fluid>
        <v-row>
            <v-col cols="12">
                <v-card class="product-card">
                    <v-card-title class="product-card-title">
                        <span class="text-h5">Sent SMS</span>
                        <v-spacer></v-spacer>
                        <v-text-field v-model="search" label="Search by mobile number" @input="filterProducts" outlined
                            dense hide-details class="search-field" color="yellow darken-2"></v-text-field>
                        <v-btn class="filter-btn" @click="filterProducts">Filter</v-btn>
                        <v-btn class="refresh-btn" @click="refreshProducts">Refresh</v-btn>
                    </v-card-title>
                    <v-data-table :headers="headers" :items="filteredProducts" class="elevation-1 product-data-table">
                        <template v-slot:[`item.sent`]="slotProps">
                            <span>{{ slotProps.item.sent ? 'SENT' : "UNSENT" }}</span>
                        </template>
                        <template v-slot:[`item.createdAt`]="slotProps">
                            <span>{{ formatDate(slotProps.item.createdAt) }}</span>
                        </template>


                    </v-data-table>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>

import { getSMS } from '../services/leads';
export default {
    data() {
        return {
            search: '',
            headers: [
                { text: 'Phone Number', value: 'destination' },
                { text: 'Message', value: 'message' },
                { text: 'Status', value: 'sent' },
                { text: 'Date Sent', value: 'createdAt' },
            ],
            products: [],
            showDialog: false,
            filteredProducts: []
        };
    },
    created() {
        this.filteredProducts = this.products;
    },
    methods: {
        formatDate(date) {
            return new Date(date).toLocaleString();
        },
        filterProducts() {
            if (this.search) {
                this.filteredProducts = this.products.filter(product =>
                    product.name.toLowerCase().includes(this.search.toLowerCase())
                );
            } else {
                this.filteredProducts = this.products;
            }
        },
        refreshProducts() {
            this.filteredProducts = [...this.products];
        },
        fetchLeads() {
            // Fetch leads from the API
            getSMS()
                .then(response => {
                    this.products = response.data
                    this.filteredProducts = this.products;
                })
                .catch(error => {
                    console.error(error);
                });
        },
    },
    mounted() {
        this.fetchLeads();
    }
};
</script>

<style scoped>
.product-card {
    background-color: #212121;
    color: #ffffff;
    border-radius: 20px;
    padding: 20px;
}

.product-card-title {
    display: flex;
    align-items: center;
    color: #f7b733;
}

.search-field {
    background-color: #424242 !important;
    border-radius: 10px;
    color: #ffffff;
    --v-theme-primary: #f7b733;
    /* Ensure primary color is correct */
}

.filter-btn,
.refresh-btn {
    background-color: #f7b733 !important;
    color: #212121 !important;
    margin-left: 10px;
    border-radius: 25px;
}

.product-data-table {
    /* background-color: #333333; */
    border-radius: 10px;
}

.edit-btn {
    color: #f7b733;
}

.delete-btn {
    color: #e53935;
}

.dialog-card {
    /* background-color: #212121; */
    color: #212121;
}

.clear-btn {
    background-color: #e53935;
    color: #212121;
}

.save-btn {
    background-color: #f7b733;
    color: #212121;
}
</style>