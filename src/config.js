const dev = {
  BACKEND_SERVICE: "http://localhost:8484/api",
  LEAD_SERVICE: "http://localhost:3030",
};

export const LEAD_SERVICE = "http://localhost:3030";

const local = {
  BACKEND_SERVICE: process.env.VUE_APP_LOCAL_NGINX,
};

const prod = {
  BACKEND_SERVICE: "https://rest-api.sandbox.phldev.co.za/api",
  LEAD_SERVICE: "https://lgfdeposit.spectrumcreditltd.com/ussd-api/",
};

const choose = {
  dev,
  prod,
  local,
};

const config = process.env.VUE_APP_STAGE
  ? choose[process.env.VUE_APP_STAGE]
  : choose["dev"];

export default config;
