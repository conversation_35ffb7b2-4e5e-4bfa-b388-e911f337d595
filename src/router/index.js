import Vue from "vue";
import Router from "vue-router";
import AdminDashboard from "@/components/AdminDashboard.vue";
import ProductManagement from "@/components/ProductManagement.vue";
import BidManagement from "@/components/BidManagement.vue";
import VehicleManagement from "@/components/VehicleManagement.vue"; // Import the Vehicle Management component
import LeadsView from "@/views/LeadsView.vue"; // Import the Vehicle Management component
import SmsView from "@/views/SmsView.vue"; // Import the Vehicle Management component
import AdminLogin from "@/views/Login.vue";
import LoanView from "@/views/LoanView.vue";

Vue.use(Router);

const router = new Router({
  mode: "history",
  routes: [
    {
      path: "/",
      name: "login",
      component: AdminLogin,
    },
    {
      path: "/dashboard",
      name: "dashboard",
      component: AdminDashboard,
      meta: { requiresAuth: true }, // Protect this route
    },
    {
      path: "/products",
      name: "products",
      component: ProductManagement,
      meta: { requiresAuth: true }, // Protect this route
    },
    {
      path: "/bids",
      name: "bids",
      component: BidManagement,
      meta: { requiresAuth: true }, // Protect this route
    },
    {
      path: "/vehicles",
      name: "vehicles",
      component: VehicleManagement,
      meta: { requiresAuth: true }, // Protect this route
    },
    {
      path: "/leads",
      name: "leads",
      component: LeadsView,
      // meta: { requiresAuth: true }, // Protect this route
    },
    {
      path: "/sms",
      name: "sms",
      component: SmsView,
      // meta: { requiresAuth: true }, // Protect this route
    },
    {
      path: "/loans",
      name: "loans",
      component: LoanView,
      // meta: { requiresAuth: true }, // Protect this route
    },
  ],
});

// Navigation guard to check for authentication
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem("authenticated");

  if (
    to.matched.some((record) => record.meta.requiresAuth) &&
    !isAuthenticated
  ) {
    next({ name: "login" }); // Redirect to login if not authenticated
  } else {
    next(); // Proceed to the route
  }
});

export default router;
