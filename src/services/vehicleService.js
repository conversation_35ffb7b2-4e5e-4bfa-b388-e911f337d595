import axios from "@/utils/axios";
// Method to fetch all vehicles
export const getVehicles = async () => {
  const { data } = await axios.get("/vehicles/searchVehicle?search=FANIKIWA");
  return data;
};

// Method to create a new vehicle
export const createVehicle = async (vehicle) => {
  console.log("vehicle", vehicle);

  const { data } = await axios.post("/vehicles", vehicle);
  return data;
};

// Method to update an existing vehicle
export const updateVehicle = async (vehicle) => {
  console.log("vehicle", vehicle);
  const { data } = await axios.put(`/vehicles/${vehicle.Id}`, vehicle);
  return data;
};

// Method to delete a vehicle by ID
export const deleteVehicle = async (id) => {
  const { data } = await axios.patch(`/vehicles/${id}`);
  return data;
};

// get auction products
export const getAuctionProducts = async () => {
  const { data } = await axios.get("/vehicles/auction?search=FANIKIWA");
  return data;
};

// create auction
export const createAuction = async (auction) => {
  const { data } = await axios.post("/vehicles/auction", auction);
  return data;
};

// get vehicle bids
export const getVehicleBids = async (search) => {
  const { data } = await axios.get(
    search ? `/vehicles/bids?search=${search}` : "/vehicles/bids"
  );
  return data;
};
