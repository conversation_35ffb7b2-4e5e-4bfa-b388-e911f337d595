import axios from "../utils/lead-axios";

export const getLeads = async () => {
  try {
    const response = await axios.get(`/lead`, {
      params: {
        $limit: 100,
        $sort: { id: -1 },
      },
    });
    console.log(response);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getSMS = async () => {
  try {
    const response = await axios.get(`/sent-sms`, {
      params: {
        $limit: 100,
        $sort: { id: -1 },
      },
    });
    console.log(response);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export const getLoan = async () => {
  try {
    const response = await axios.get(`/loan`, {
      params: {
        $limit: 100,
        $sort: { id: -1 },
      },
    });
    console.log(response);
    return response.data;
  } catch (error) {
    console.error(error);
  }
};
