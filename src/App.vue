<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer app permanent color="#212121">
      <v-list dense>
        <v-list-item link :to="{ name: 'dashboard' }">
          <v-list-item-icon>
            <v-icon color="#f7b733">mdi-view-dashboard</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="drawer-item-title">Dashboard</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item link :to="{ name: 'leads' }">
          <v-list-item-icon>
            <v-icon color="#f7b733">mdi-account-filter</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="drawer-item-title">Leads</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item link :to="{ name: 'vehicles' }">
          <v-list-item-icon>
            <v-icon color="#f7b733">mdi-car</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="drawer-item-title">Vehicles</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item link :to="{ name: 'products' }">
          <v-list-item-icon>
            <v-icon color="#f7b733">mdi-cart</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="drawer-item-title">Auction</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item link :to="{ name: 'bids' }">
          <v-list-item-icon>
            <v-icon color="#f7b733">mdi-gavel</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="drawer-item-title">Bids</v-list-item-title>
          </v-list-item-content>
        </v-list-item>

        <v-list-item link :to="{ name: 'loans' }">
          <v-list-item-icon>
            <v-icon color="#f7b733">mdi-cash</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="drawer-item-title">Applied Loans</v-list-item-title>
          </v-list-item-content>
        </v-list-item>

        <v-list-item link :to="{ name: 'sms' }">
          <v-list-item-icon>
            <v-icon color="#f7b733">mdi-message-processing</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="drawer-item-title">Sent SMS</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar app color="#212121" dark>
      <v-toolbar-title class="title text-h5 font-weight-bold">
        <v-icon left class="mr-2" color="#f7b733">mdi-gavel</v-icon>
        Bidding App Admin
      </v-toolbar-title>
      <v-spacer></v-spacer>
      <v-avatar size="32" class="mr-2">
        <img src="https://randomuser.me/api/portraits/lego/2.jpg" alt="Avatar" />
      </v-avatar>
    </v-app-bar>

    <!-- Main Content Area -->
    <v-main>
      <v-container fluid class="content-area">
        <router-view></router-view>
      </v-container>
    </v-main>
  </v-app>
</template>

<script>
export default {
  name: 'App',
};
</script>

<style scoped>
.title {
  color: #f7b733;
}

.v-navigation-drawer {
  background: #212121;
}

.drawer-item-title {
  color: #ffffff;
  /* Ensures visibility against the dark background */
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
}

.v-app-bar {
  box-shadow: none;
  background-color: #212121;
}

.v-container {
  background-color: #333333;
  padding: 16px;
  border-radius: 8px;
  color: #ffffff;
}

.v-avatar img {
  border-radius: 50%;
  border: 2px solid #f7b733;
}

.v-icon {
  color: #f7b733;
}
</style>
