import swal from "sweetalert";

export const successAlert = (message) => {
  swal({
    title: "Success",
    text: message,
    icon: "success",
    button: "OK",
  });
};

export const errorAlert = (message) => {
  swal({
    title: "Error",
    text: message,
    icon: "error",
    button: "OK",
  });
};
export const warningAlert = (message) => {
  swal({
    title: "Warning",
    text: message,
    icon: "warning",
    button: "OK",
  });
};
export const infoAlert = (message) => {
  swal({
    title: "Info",
    text: message,
    icon: "info",
    button: "OK",
  });
};
export const confirmAlert = (message, callback) => {
  swal({
    title: "Are you sure?",
    text: message,
    icon: "warning",
    buttons: true,
    dangerMode: true,
  }).then((willDelete) => {
    if (willDelete) {
      callback();
    }
  });
};
